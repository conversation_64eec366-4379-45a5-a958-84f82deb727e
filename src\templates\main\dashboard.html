{% extends "base.html" %}

{% block title %}Dashboard - FurEverMemories{% endblock %}

{% block styles %}
<style>
    /* Modern Dashboard Styling */
    .dashboard-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .dashboard-header {
        text-align: center;
        margin-bottom: 3rem;
        padding: 2rem 0;
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .dashboard-header h1 {
        color: var(--primary-color);
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
        font-weight: 700;
    }

    .dashboard-header p {
        color: #6c757d;
        font-size: 1.1rem;
        margin: 0;
    }

    /* Quick Actions Cards */
    .quick-actions {
        margin-bottom: 3rem;
    }

    .action-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: none;
        height: 100%;
    }

    .action-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .action-card .icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: white;
        font-size: 1.5rem;
    }

    .action-card h5 {
        color: var(--primary-color);
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .action-card p {
        color: #6c757d;
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }

    /* Content Sections */
    .content-section {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        min-height: 400px;
    }

    .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .section-title {
        color: var(--primary-color);
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: var(--secondary-color);
    }

    .view-all-btn {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        background: rgba(91, 140, 133, 0.1);
        transition: all 0.3s ease;
    }

    .view-all-btn:hover {
        background: var(--primary-color);
        color: white;
        text-decoration: none;
    }

    /* Upload Grid */
    .dashboard-upload-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
        gap: 1.5rem;
        margin-top: 1rem;
    }

    .dashboard-upload-item {
        position: relative;
        aspect-ratio: 1;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .dashboard-upload-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .dashboard-upload-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .dashboard-upload-item .video-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 2rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    }

    .dashboard-upload-item .remove-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        background-color: rgba(220, 53, 69, 0.9);
        color: white;
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        line-height: 1;
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 10;
        backdrop-filter: blur(4px);
    }

    .dashboard-upload-item:hover .remove-btn {
        opacity: 1;
    }

    .dashboard-upload-item .remove-btn:hover {
        background-color: rgba(220, 53, 69, 1);
        transform: scale(1.1);
    }

    /* Video Cards */
    .video-card {
        position: relative;
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
    }

    .video-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    }

    .video-card .remove-btn {
        position: absolute;
        top: 15px;
        right: 15px;
        background-color: rgba(220, 53, 69, 0.9);
        color: white;
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        line-height: 1;
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 10;
        backdrop-filter: blur(4px);
    }

    .video-card:hover .remove-btn {
        opacity: 1;
    }

    .video-card .remove-btn:hover {
        background-color: rgba(220, 53, 69, 1);
        transform: scale(1.1);
    }

    .video-card .card-title {
        color: var(--primary-color);
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .video-status {
        display: inline-block;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .status-pending {
        background: linear-gradient(135deg, #ffeeba, #fff3cd);
        color: #856404;
    }

    .status-processing {
        background: linear-gradient(135deg, #b8daff, #cce7ff);
        color: #004085;
    }

    .status-completed {
        background: linear-gradient(135deg, #c3e6cb, #d4edda);
        color: #155724;
    }

    .status-failed {
        background: linear-gradient(135deg, #f5c6cb, #f8d7da);
        color: #721c24;
    }

    /* Empty States */
    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 3rem;
        color: var(--secondary-color);
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 1rem 0;
        }

        .dashboard-header {
            margin-bottom: 1.5rem;
            padding: 1.5rem;
        }

        .dashboard-header h1 {
            font-size: 2rem;
        }

        .content-section {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .dashboard-upload-grid {
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 1rem;
        }

        .action-card {
            padding: 1.5rem;
        }
    }

    /* Loading States */
    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 3rem;
    }

    .spinner-border {
        color: var(--primary-color);
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="container">
        <!-- Hidden CSRF token for AJAX requests -->
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1>Welcome to Your Dashboard</h1>
            <p>Manage your uploads, create memorial videos, and honor your beloved pet's memory</p>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="action-card">
                        <div class="icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h5>Upload Media</h5>
                        <p>Upload photos and videos of your beloved pet to create lasting memories.</p>
                        <a href="{{ url_for('upload.upload_files') }}" class="btn btn-primary">Upload Files</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="action-card">
                        <div class="icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h5>Create Memorial Video</h5>
                        <p>Transform your uploads into a beautiful AI-generated tribute video.</p>
                        <a href="{{ url_for('video.templates') }}" class="btn btn-primary">Create Video</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="action-card">
                        <div class="icon">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <h5>Account Settings</h5>
                        <p>Manage your account details, preferences, and subscription settings.</p>
                        <a href="#" class="btn btn-outline-primary">Manage Account</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Sections -->
        <div class="row g-4">
            <!-- Your Uploads Section -->
            <div class="col-lg-6">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-images"></i>
                            Your Uploads
                        </h2>
                    </div>

                    <div id="uploadsContainer">
                        <div class="loading-spinner" id="uploadsLoading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div class="d-none" id="noUploadsMessage">
                            <div class="empty-state">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <h5>No uploads yet</h5>
                                <p>Start by uploading some photos or videos of your beloved pet.</p>
                                <a href="{{ url_for('upload.upload_files') }}" class="btn btn-primary">Upload Files</a>
                            </div>
                        </div>
                        <div class="dashboard-upload-grid" id="uploadsGrid"></div>
                    </div>
                </div>
            </div>

            <!-- Your Videos Section -->
            <div class="col-lg-6">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-play-circle"></i>
                            Your Videos
                        </h2>
                    </div>

                    <div id="videosContainer">
                        <div class="loading-spinner" id="videosLoading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div class="d-none" id="noVideosMessage">
                            <div class="empty-state">
                                <i class="fas fa-video"></i>
                                <h5>No videos yet</h5>
                                <p>Create your first memorial video to honor your pet's memory.</p>
                                <a href="{{ url_for('video.templates') }}" class="btn btn-primary">Create Video</a>
                            </div>
                        </div>
                        <div id="videosList"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load user uploads
    loadUserUploads();

    // Load user videos
    loadUserVideos();

    // Function to load user uploads
    function loadUserUploads() {
        fetch('/api/uploads')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('uploadsGrid');
                const loadingElement = document.getElementById('uploadsLoading');
                const noUploadsMessage = document.getElementById('noUploadsMessage');

                loadingElement.remove();

                if (data.length === 0) {
                    noUploadsMessage.classList.remove('d-none');
                    return;
                }

                // Display only the most recent 8 uploads
                const recentUploads = data.slice(0, 8);

                recentUploads.forEach(upload => {
                    const uploadItem = document.createElement('div');
                    uploadItem.className = 'dashboard-upload-item';
                    uploadItem.setAttribute('data-upload-id', upload.id);

                    if (upload.file_type === 'image') {
                        const img = document.createElement('img');
                        img.src = upload.url;
                        img.alt = upload.file_name;
                        uploadItem.appendChild(img);
                    } else {
                        // Video placeholder
                        uploadItem.style.backgroundColor = '#343a40';
                        const videoIndicator = document.createElement('div');
                        videoIndicator.className = 'video-indicator';
                        videoIndicator.innerHTML = '<i class="fas fa-film"></i>';
                        uploadItem.appendChild(videoIndicator);
                    }

                    // Add delete button
                    const deleteBtn = document.createElement('div');
                    deleteBtn.className = 'remove-btn';
                    deleteBtn.innerHTML = '×'; // Use HTML entity for X instead of FontAwesome
                    deleteBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        if (confirm('Are you sure you want to delete this file?')) {
                            deleteUpload(upload.id);
                        }
                    });
                    uploadItem.appendChild(deleteBtn);

                    container.appendChild(uploadItem);
                });

                if (data.length > 8) {
                    const viewAllContainer = document.createElement('div');
                    viewAllContainer.className = 'text-center mt-3';

                    const viewAllLink = document.createElement('a');
                    viewAllLink.href = '{{ url_for("upload.upload_files") }}';
                    viewAllLink.className = 'btn btn-outline-primary';
                    viewAllLink.textContent = `View All (${data.length})`;

                    viewAllContainer.appendChild(viewAllLink);
                    container.parentNode.appendChild(viewAllContainer);
                }
            })
            .catch(error => {
                console.error('Error loading uploads:', error);
                const loadingElement = document.getElementById('uploadsLoading');

                loadingElement.remove();

                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger';
                errorMessage.textContent = 'There was an error loading your uploads. Please try refreshing the page.';

                document.getElementById('uploadsContainer').appendChild(errorMessage);
            });
    }

    // Function to load user videos
    function loadUserVideos() {
        fetch('/api/videos')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('videosList');
                const loadingElement = document.getElementById('videosLoading');
                const noVideosMessage = document.getElementById('noVideosMessage');

                loadingElement.remove();

                if (data.length === 0) {
                    noVideosMessage.classList.remove('d-none');
                    return;
                }

                data.forEach(video => {
                const card = document.createElement('div');
                card.className = 'video-card';
                card.setAttribute('data-video-id', video.id);

                // Create the main content
                const title = document.createElement('div');
                title.className = 'card-title';
                title.textContent = video.title || 'Memorial Video';

                const statusBadge = document.createElement('div');
                statusBadge.className = `video-status status-${video.status}`;
                statusBadge.textContent = video.status.charAt(0).toUpperCase() + video.status.slice(1);

                const createdDate = document.createElement('p');
                createdDate.className = 'text-muted mb-3';
                createdDate.innerHTML = `<small><i class="fas fa-calendar-alt me-1"></i>Created: ${new Date(video.created_at).toLocaleDateString()}</small>`;

                // Create action content based on status
                const actionContainer = document.createElement('div');

                if (video.status === 'completed') {
                    actionContainer.innerHTML = `
                        <div class="d-flex gap-2">
                            <a href="/video/view/${video.view_token}" class="btn btn-primary btn-sm">
                                <i class="fas fa-play me-1"></i>View Video
                            </a>
                            <button class="btn btn-outline-secondary btn-sm" onclick="shareVideo('/video/view/${video.view_token}')">
                                <i class="fas fa-share me-1"></i>Share
                            </button>
                        </div>
                    `;
                } else if (video.status === 'processing') {
                    actionContainer.innerHTML = `
                        <div class="text-center py-3">
                            <div class="spinner-border spinner-border-sm text-primary mb-2" role="status"></div>
                            <p class="text-muted mb-0">Your video is being generated...</p>
                        </div>
                    `;
                } else if (video.status === 'pending') {
                    actionContainer.innerHTML = `
                        <a href="/payment/${video.id}" class="btn btn-warning btn-sm">
                            <i class="fas fa-credit-card me-1"></i>Complete Payment
                        </a>
                    `;
                } else if (video.status === 'failed') {
                    actionContainer.innerHTML = `
                        <div class="alert alert-danger py-2 mb-0">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Video generation failed. Please contact support.
                        </div>
                    `;
                }

                // Append all elements to card
                card.appendChild(title);
                card.appendChild(statusBadge);
                card.appendChild(createdDate);
                card.appendChild(actionContainer);

                // Add delete button for pending videos only
                if (video.status === 'pending') {
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'remove-btn';
                    deleteBtn.innerHTML = '×';
                    deleteBtn.title = 'Delete video';
                    deleteBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        if (confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
                            deleteVideo(video.id);
                        }
                    });

                    card.appendChild(deleteBtn);
                }

                container.appendChild(card);
            });
        })
        .catch(error => {
            console.error('Error loading videos:', error);
            const loadingElement = document.getElementById('videosLoading');

            loadingElement.remove();

            const errorMessage = document.createElement('div');
            errorMessage.className = 'alert alert-danger';
            errorMessage.textContent = 'There was an error loading your videos. Please try refreshing the page.';

            document.getElementById('videosContainer').appendChild(errorMessage);
        });
    }

    // Function to delete an upload
    function deleteUpload(uploadId) {
        // Find the upload item and show loading state
        const uploadCard = document.querySelector(`[data-upload-id="${uploadId}"]`);
        if (uploadCard) {
            uploadCard.style.opacity = '0.5';
            uploadCard.style.pointerEvents = 'none';
        }

        fetch(`/api/uploads/${uploadId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success alert-dismissible fade show';
                successMessage.innerHTML = `
                    File deleted successfully!
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                // Insert the message at the top of the container
                const container = document.querySelector('.container');
                container.insertBefore(successMessage, container.firstChild);

                // Auto-remove after 3 seconds
                setTimeout(() => {
                    if (successMessage.parentNode) {
                        successMessage.remove();
                    }
                }, 3000);

                // Reload the uploads to update the display
                loadUserUploads();
            } else {
                // Restore the card state
                if (uploadCard) {
                    uploadCard.style.opacity = '1';
                    uploadCard.style.pointerEvents = 'auto';
                }
                alert('Failed to delete file: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error deleting upload:', error);
            // Restore the card state
            if (uploadCard) {
                uploadCard.style.opacity = '1';
                uploadCard.style.pointerEvents = 'auto';
            }
            alert('Failed to delete file. Please try again.');
        });
    }

    // Function to delete a video
    function deleteVideo(videoId) {
        // Find the video card and show loading state
        const videoCard = document.querySelector(`[data-video-id="${videoId}"]`);
        if (videoCard) {
            videoCard.style.opacity = '0.5';
            videoCard.style.pointerEvents = 'none';
        }

        fetch(`/api/videos/${videoId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the video card from the UI
                if (videoCard) {
                    videoCard.remove();
                }

                // Check if there are any videos left
                const container = document.getElementById('videosList');
                if (container.children.length === 0) {
                    const noVideosMessage = document.getElementById('noVideosMessage');
                    if (noVideosMessage) {
                        noVideosMessage.classList.remove('d-none');
                    }
                }
            } else {
                // Restore the card state
                if (videoCard) {
                    videoCard.style.opacity = '1';
                    videoCard.style.pointerEvents = 'auto';
                }
                alert('Failed to delete video: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error deleting video:', error);
            // Restore the card state
            if (videoCard) {
                videoCard.style.opacity = '1';
                videoCard.style.pointerEvents = 'auto';
            }
            alert('Failed to delete video. Please try again.');
        });
    }

    // Function to share video
    function shareVideo(videoUrl) {
        const fullUrl = window.location.origin + videoUrl;

        if (navigator.share) {
            navigator.share({
                title: 'Memorial Video - FurEverMemories',
                text: 'Check out this beautiful memorial video',
                url: fullUrl
            }).catch(console.error);
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(fullUrl).then(() => {
                // Show temporary success message
                const toast = document.createElement('div');
                toast.className = 'alert alert-success position-fixed';
                toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
                toast.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    Video link copied to clipboard!
                `;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.remove();
                }, 3000);
            }).catch(() => {
                alert('Video URL: ' + fullUrl);
            });
        }
    }
});
</script>
{% endblock %}
